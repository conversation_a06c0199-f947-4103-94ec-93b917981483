import _ from 'lodash';

export const DraftManager = {
    updateDraft: function (draftParams, files, section) {
        let {
            fieldValues: data,
            update_type_id,
            isDynamicForm,
            hasFeatureAccess,
        } = draftParams;
        if (
            update_type_id == 'sbtsk_can_postpone' ||
            update_type_id == 'sbtsk_can_reject' ||
            (isDynamicForm == true && !hasFeatureAccess)
        ) {
            return;
        }
        let localStorageKey = this.getDraftKey(draftParams);
        // let data = this.formRef?.current.getFieldsValue();
        let existingDraft = localStorage.getItem(localStorageKey);
        existingDraft = JSON.parse(existingDraft);

        function createDraftFromUpdates(currentData, updates) {
            //section is attachment based fields
            if (section) {
                // Iterate over each category in updates.section
                for (const category in updates[section]) {
                    // Check if the category exists in updates
                    if (updates[section].hasOwnProperty(category)) {
                        if (!currentData?.[section]) {
                            currentData = {
                                ...currentData,
                                [section]: {},
                            };
                        }
                        if (!currentData?.[section]?.category) {
                            currentData = {
                                ...currentData,
                                [section]: {
                                    ...currentData?.[section],
                                    [category]: [],
                                },
                            };
                        }
                        const currentItems = currentData?.[section]?.[category];
                        const updatedItems = updates?.[section]?.[category];
                        // More items in updates means we replace; fewer means we filter
                        if (
                            (updatedItems?.length || 0) >
                            (currentItems?.length || 0)
                        ) {
                            currentData[section][category] = updatedItems;
                            // break;
                        } else if (
                            (updatedItems?.length || 0) <
                            (currentItems?.length || 0)
                        ) {
                            currentData[section][category] =
                                currentItems.filter((item) =>
                                    updatedItems.includes(item)
                                );
                            // break;
                        }
                    }
                }
            } else {
                for (const category in updates) {
                    if (category != section) {
                        currentData[category] = updates[category];
                    }
                }
            }

            return currentData;
        }

        if (!existingDraft) {
            existingDraft = {};
        }

        data = _.merge(data, this.getFilesSectionsFormData2(files, section));
        const updatedDraft = createDraftFromUpdates(existingDraft, data);
        localStorage.setItem(localStorageKey, JSON.stringify(updatedDraft));
    },

    applyDraft: function (draftParams, forceApplyForDynamicForm = false) {
        const { isDynamicForm } = draftParams;

        if (isDynamicForm == true && !forceApplyForDynamicForm) {
            return false;
        }

        let localStorageKey = this.getDraftKey(draftParams);
        let draftData = localStorage.getItem(localStorageKey);

        if (draftData) {
            draftData = JSON.parse(draftData);
        }

        return draftData;
    },

    clearDraft: function (draftParams) {
        localStorage.removeItem(this.getDraftKey(draftParams));
    },

    getDraftKey: function (draftParams) {
        let { sbtsk_id, update_type_id } = draftParams;
        return `sbtskDraftData_${sbtsk_id}_${update_type_id}`; //make it a method
    },

    getFilesSectionsFormData: function (draftParams) {
        const {
            filesBySection,
            micRecordingsBySection,
            cameraRecordingsBySection,
        } = draftParams.state;
        // debugger;
        let data = {};

        // Function to remove duplicates from arrays within an object
        function removeDuplicatesFromObject(obj) {
            for (let key in obj) {
                if (Array.isArray(obj[key])) {
                    obj[key] = [...new Set(obj[key])]; // Remove duplicates
                }
            }
            return obj;
        }

        // Remove duplicates from each section
        data['attachments'] = removeDuplicatesFromObject(filesBySection);
        data['mic_files'] = removeDuplicatesFromObject(micRecordingsBySection);
        data['camera_files'] = removeDuplicatesFromObject(
            cameraRecordingsBySection
        );

        return data;
    },

    getFilesSectionsFormData2: function (files, section) {
        // debugger;
        let data = {};

        // Function to remove duplicates from arrays within an object
        function removeDuplicatesFromObject(obj) {
            for (let key in obj) {
                if (Array.isArray(obj[key])) {
                    obj[key] = [...new Set(obj[key])]; // Remove duplicates
                }
            }
            return obj;
        }

        // Remove duplicates from each section
        data[section] = removeDuplicatesFromObject(files);
        return data;
    },
};
